('E:\\VSCodeProjects\\check_nexon_account\\build\\main\\PYZ-00.pyz',
 [('__future__', 'E:\\Python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\Python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\Python\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'E:\\Python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\Python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'E:\\Python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'E:\\Python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'E:\\Python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'E:\\Python\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'E:\\Python\\lib\\base64.py', 'PYMODULE'),
  ('bcrypt', 'E:\\Python\\lib\\site-packages\\bcrypt\\__init__.py', 'PYMODULE'),
  ('bisect', 'E:\\Python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'E:\\Python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'E:\\Python\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\Python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\Python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('chardet',
   'E:\\Python\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'E:\\Python\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'E:\\Python\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'E:\\Python\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'E:\\Python\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'E:\\Python\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'E:\\Python\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'E:\\Python\\lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'E:\\Python\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'E:\\Python\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'E:\\Python\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'E:\\Python\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'E:\\Python\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'E:\\Python\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'E:\\Python\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'E:\\Python\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'E:\\Python\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'E:\\Python\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'E:\\Python\\lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'E:\\Python\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'E:\\Python\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'E:\\Python\\lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'E:\\Python\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'E:\\Python\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'E:\\Python\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'E:\\Python\\lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'E:\\Python\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'E:\\Python\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'E:\\Python\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'E:\\Python\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'E:\\Python\\lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'E:\\Python\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'E:\\Python\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('configparser', 'E:\\Python\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'E:\\Python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'E:\\Python\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'E:\\Python\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'E:\\Python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\Python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\Python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\Python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'E:\\Python\\lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'E:\\Python\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'E:\\Python\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'E:\\Python\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'E:\\Python\\lib\\dis.py', 'PYMODULE'),
  ('email', 'E:\\Python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\Python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\Python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'E:\\Python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'E:\\Python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\Python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\Python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\Python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\Python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\Python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\Python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'E:\\Python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'E:\\Python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\Python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\Python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'E:\\Python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'E:\\Python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\Python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\Python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'E:\\Python\\lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'E:\\Python\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'E:\\Python\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'E:\\Python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\Python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\Python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\Python\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'E:\\Python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\Python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'E:\\Python\\lib\\hmac.py', 'PYMODULE'),
  ('http', 'E:\\Python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\Python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\Python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'E:\\Python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('idna', 'E:\\Python\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'E:\\Python\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'E:\\Python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\Python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\Python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\Python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'E:\\Python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\Python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\Python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common', 'E:\\Python\\lib\\importlib\\_common.py', 'PYMODULE'),
  ('importlib.abc', 'E:\\Python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\Python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata', 'E:\\Python\\lib\\importlib\\metadata.py', 'PYMODULE'),
  ('importlib.resources',
   'E:\\Python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\Python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'E:\\Python\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'E:\\Python\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'E:\\Python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'E:\\Python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'E:\\Python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\Python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'E:\\Python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'E:\\Python\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'E:\\Python\\lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'E:\\Python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\Python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'E:\\Python\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'E:\\Python\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'E:\\Python\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'E:\\Python\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'E:\\Python\\lib\\pickle.py', 'PYMODULE'),
  ('platform', 'E:\\Python\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'E:\\Python\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'E:\\Python\\lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'E:\\Python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'E:\\Python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'E:\\Python\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'E:\\Python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\Python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\Python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\Python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\Python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\Python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\Python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\Python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\Python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\Python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\Python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\Python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\Python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\Python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\Python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\Python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\Python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors', 'E:\\Python\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'E:\\Python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\Python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\Python\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'E:\\Python\\lib\\socket.py', 'PYMODULE'),
  ('socks', 'E:\\Python\\lib\\site-packages\\socks.py', 'PYMODULE'),
  ('ssl', 'E:\\Python\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'E:\\Python\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'E:\\Python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\Python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\Python\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'E:\\Python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\Python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\Python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\Python\\lib\\threading.py', 'PYMODULE'),
  ('token', 'E:\\Python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'E:\\Python\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\Python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'E:\\Python\\lib\\typing.py', 'PYMODULE'),
  ('urllib', 'E:\\Python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'E:\\Python\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\Python\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'E:\\Python\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'E:\\Python\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'E:\\Python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\Python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\Python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\Python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\Python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\Python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\Python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\Python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\Python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'E:\\Python\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\Python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'E:\\Python\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'E:\\Python\\lib\\uuid.py', 'PYMODULE'),
  ('zipfile', 'E:\\Python\\lib\\zipfile.py', 'PYMODULE')])
