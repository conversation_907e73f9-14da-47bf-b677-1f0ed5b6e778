(['E:\\VSCodeProjects\\check_nexon_account\\main.py'],
 ['E:\\VSCodeProjects\\check_nexon_account'],
 [],
 ['E:\\Python\\Lib\\site-packages\\numpy\\_pyinstaller',
  'E:\\Python\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
  'E:\\Python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'E:\\Python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 {},
 [],
 [],
 '3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('main', 'E:\\VSCodeProjects\\check_nexon_account\\main.py', 'PYSOURCE')],
 [('inspect', 'E:\\Python\\lib\\inspect.py', 'PYMODULE'),
  ('importlib', 'E:\\Python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._common', 'E:\\Python\\lib\\importlib\\_common.py', 'PYMODULE'),
  ('contextlib', 'E:\\Python\\lib\\contextlib.py', 'PYMODULE'),
  ('tempfile', 'E:\\Python\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'E:\\Python\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'E:\\Python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'E:\\Python\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'E:\\Python\\lib\\_compression.py', 'PYMODULE'),
  ('copy', 'E:\\Python\\lib\\copy.py', 'PYMODULE'),
  ('struct', 'E:\\Python\\lib\\struct.py', 'PYMODULE'),
  ('lzma', 'E:\\Python\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'E:\\Python\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'E:\\Python\\lib\\fnmatch.py', 'PYMODULE'),
  ('zipfile', 'E:\\Python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'E:\\Python\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.util', 'E:\\Python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib.abc', 'E:\\Python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('typing', 'E:\\Python\\lib\\typing.py', 'PYMODULE'),
  ('pathlib', 'E:\\Python\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\Python\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib', 'E:\\Python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\Python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata', 'E:\\Python\\lib\\importlib\\metadata.py', 'PYMODULE'),
  ('configparser', 'E:\\Python\\lib\\configparser.py', 'PYMODULE'),
  ('email', 'E:\\Python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'E:\\Python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase', 'E:\\Python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.utils', 'E:\\Python\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'E:\\Python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'E:\\Python\\lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'E:\\Python\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'E:\\Python\\lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'E:\\Python\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'E:\\Python\\lib\\selectors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\Python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.message', 'E:\\Python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'E:\\Python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\Python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'E:\\Python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\Python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\Python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'E:\\Python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\Python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'E:\\Python\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'E:\\Python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'E:\\Python\\lib\\gettext.py', 'PYMODULE'),
  ('quopri', 'E:\\Python\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'E:\\Python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'E:\\Python\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'E:\\Python\\lib\\textwrap.py', 'PYMODULE'),
  ('email._header_value_parser',
   'E:\\Python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header', 'E:\\Python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\Python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\Python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'E:\\Python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\Python\\lib\\email\\errors.py', 'PYMODULE'),
  ('csv', 'E:\\Python\\lib\\csv.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\Python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse', 'E:\\Python\\lib\\argparse.py', 'PYMODULE'),
  ('token', 'E:\\Python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'E:\\Python\\lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\Python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'E:\\Python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'E:\\Python\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'E:\\Python\\lib\\ast.py', 'PYMODULE'),
  ('stringprep', 'E:\\Python\\lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'E:\\Python\\lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\Python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('pickle', 'E:\\Python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'E:\\Python\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\Python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('string', 'E:\\Python\\lib\\string.py', 'PYMODULE'),
  ('random', 'E:\\Python\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'E:\\Python\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'E:\\Python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\Python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'E:\\Python\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'E:\\Python\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'E:\\Python\\lib\\numbers.py', 'PYMODULE'),
  ('bisect', 'E:\\Python\\lib\\bisect.py', 'PYMODULE'),
  ('uuid', 'E:\\Python\\lib\\uuid.py', 'PYMODULE'),
  ('subprocess', 'E:\\Python\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'E:\\Python\\lib\\signal.py', 'PYMODULE'),
  ('platform', 'E:\\Python\\lib\\platform.py', 'PYMODULE'),
  ('hashlib', 'E:\\Python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'E:\\Python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('queue', 'E:\\Python\\lib\\queue.py', 'PYMODULE'),
  ('threading', 'E:\\Python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'E:\\Python\\lib\\_threading_local.py', 'PYMODULE'),
  ('json', 'E:\\Python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'E:\\Python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'E:\\Python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\Python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('requests',
   'E:\\Python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\Python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\Python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\Python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('urllib.request', 'E:\\Python\\lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'E:\\Python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'E:\\Python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'E:\\Python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'E:\\Python\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'E:\\Python\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'E:\\Python\\lib\\mimetypes.py', 'PYMODULE'),
  ('urllib.response', 'E:\\Python\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'E:\\Python\\lib\\urllib\\error.py', 'PYMODULE'),
  ('http.client', 'E:\\Python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookies', 'E:\\Python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\Python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'E:\\Python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('requests.models',
   'E:\\Python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna', 'E:\\Python\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.intranges',
   'E:\\Python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'E:\\Python\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'E:\\Python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\Python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\Python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\Python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\Python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\Python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\Python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('hmac', 'E:\\Python\\lib\\hmac.py', 'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('__future__', 'E:\\Python\\lib\\__future__.py', 'PYMODULE'),
  ('urllib3.filepost',
   'E:\\Python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\Python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\Python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\Python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\Python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\Python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress', 'E:\\Python\\lib\\ipaddress.py', 'PYMODULE'),
  ('urllib3.util.queue',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\Python\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\Python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.request',
   'E:\\Python\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\Python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\Python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\Python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('socks', 'E:\\Python\\lib\\site-packages\\socks.py', 'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\Python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\Python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\Python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\Python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'E:\\Python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\Python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\Python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\Python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\Python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\Python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\Python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt', 'E:\\Python\\lib\\site-packages\\bcrypt\\__init__.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('dataclasses', 'E:\\Python\\lib\\dataclasses.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\Python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\Python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\Python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'E:\\Python\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\Python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('ssl', 'E:\\Python\\lib\\ssl.py', 'PYMODULE'),
  ('chardet',
   'E:\\Python\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'E:\\Python\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'E:\\Python\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'E:\\Python\\lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'E:\\Python\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'E:\\Python\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'E:\\Python\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'E:\\Python\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'E:\\Python\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'E:\\Python\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'E:\\Python\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'E:\\Python\\lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'E:\\Python\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'E:\\Python\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'E:\\Python\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'E:\\Python\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'E:\\Python\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'E:\\Python\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'E:\\Python\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'E:\\Python\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'E:\\Python\\lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'E:\\Python\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'E:\\Python\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'E:\\Python\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'E:\\Python\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'E:\\Python\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'E:\\Python\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'E:\\Python\\lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'E:\\Python\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'E:\\Python\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'E:\\Python\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'E:\\Python\\lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.enums',
   'E:\\Python\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'E:\\Python\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'E:\\Python\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\Python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\Python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE')],
 [('python39.dll', 'E:\\Python\\python39.dll', 'BINARY'),
  ('_lzma.pyd', 'E:\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\Python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'E:\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'E:\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'E:\\Python\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'E:\\Python\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\Python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'E:\\Python\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'E:\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'E:\\Python\\lib\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'E:\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'E:\\Python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('python3.dll', 'E:\\Python\\python3.dll', 'BINARY'),
  ('libssl-1_1.dll', 'E:\\Python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('ucrtbase.dll',
   'E:\\Windows Kits\\10\\Windows Performance Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'E:\\VSCodeProjects\\check_nexon_account\\build\\main\\base_library.zip',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\Python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\Python\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\LICENSE.APACHE',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\REQUESTED',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\top_level.txt',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\WHEEL',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\LICENSE',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\METADATA',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\INSTALLER',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\LICENSE.BSD',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.5.dist-info\\RECORD',
   'E:\\Python\\lib\\site-packages\\cryptography-42.0.5.dist-info\\RECORD',
   'DATA')])
